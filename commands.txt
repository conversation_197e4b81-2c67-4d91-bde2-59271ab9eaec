
# default docker image at localhost
docker run --name ome -d -e OME_HOST_IP=localhost \
-p 1935:1935 -p 9999:9999/udp -p 9000:9000 -p 8080:3333 -p 3478:3478 -p 10000-10009:10000-10009/udp \
airensoft/ovenmediaengine:latest

#Docker image for ui
docker run -d -p 8090:80 airensoft/ovenplayerdemo:latest

# custamized conf
docker run --name custum-ome -d -e OME_HOST_IP=localhost \
  -p 1935:1935 -p 8080:8080 \
  -v $(pwd)/ome_conf:/opt/ovenmediaengine/bin/origin_conf \
  -v $(pwd)/recordings:/recordings \
  airensoft/ovenmediaengine:latest